export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

export interface LoginRequest {
  identifier: string; // email or mobile
  password?: string;
  requestOtp?: boolean;
}

export interface OTPVerificationRequest {
  identifier: string;
  otp: string;
}

export interface ChatRequest {
  message: string;
  sessionId?: string;
  llmModel?: string;
}

export interface ChatResponse {
  response: string;
  sessionId: string;
  messageId: string;
  isGuest: boolean;
  remainingGuestChats?: number;
}

export interface UserAttributes {
  id: string;
  email?: string;
  mobile?: string;
  password?: string;
  isActive: boolean;
  isVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatAttributes {
  id: string;
  userId?: string;
  sessionId: string;
  title?: string;
  isGuest: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessageAttributes {
  id: string;
  chatId: string;
  message: string;
  response: string;
  llmModel: string;
  isUserMessage: boolean;
  createdAt: Date;
}

export interface UserOTPAttributes {
  id: string;
  userId: string;
  otp: string;
  expiresAt: Date;
  isUsed: boolean;
  createdAt: Date;
}

export interface GuestSessionAttributes {
  id: string;
  sessionId: string;
  messageCount: number;
  ipAddress: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface JWTPayload {
  userId: string;
  email?: string;
  mobile?: string;
  iat: number;
  exp: number;
}

export interface LLMConfig {
  model: string;
  temperature?: number;
  maxTokens?: number;
  apiKey?: string;
}

export enum LLMProvider {
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic'
}

import { Request } from 'express';

export interface AuthenticatedRequest extends Request {
  user?: JWTPayload;
}
