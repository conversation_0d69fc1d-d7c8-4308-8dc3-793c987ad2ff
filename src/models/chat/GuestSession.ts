import { DataTypes, Model, Optional, Op, fn, col } from 'sequelize';
import { chatDatabase } from '../../config/database';
import { GuestSessionAttributes } from '../../types';

interface GuestSessionCreationAttributes extends Optional<GuestSessionAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class GuestSession extends Model<GuestSessionAttributes, GuestSessionCreationAttributes> implements GuestSessionAttributes {
  public id!: string;
  public sessionId!: string;
  public messageCount!: number;
  public ipAddress!: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Instance methods
  public async incrementMessageCount(): Promise<void> {
    this.messageCount += 1;
    await this.save();
  }

  public hasReachedLimit(limit: number = 5): boolean {
    return this.messageCount >= limit;
  }

  public getRemainingMessages(limit: number = 5): number {
    return Math.max(0, limit - this.messageCount);
  }

  // Static methods
  static async createSession(sessionId: string, ipAddress: string): Promise<GuestSession> {
    const { EncryptionUtil } = await import('../../utils/encryption');

    return GuestSession.create({
      id: EncryptionUtil.generateUUID(),
      sessionId,
      messageCount: 0,
      ipAddress,
    });
  }

  static async findBySessionId(sessionId: string): Promise<GuestSession | null> {
    return GuestSession.findOne({
      where: { sessionId },
    });
  }

  static async findOrCreateSession(sessionId: string, ipAddress: string): Promise<[GuestSession, boolean]> {
    const [session, created] = await GuestSession.findOrCreate({
      where: { sessionId },
      defaults: {
        id: (await import('../../utils/encryption')).EncryptionUtil.generateUUID(),
        sessionId,
        messageCount: 0,
        ipAddress,
      },
    });
    return [session, created];
  }

  static async cleanupOldSessions(daysOld: number = 7): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    return GuestSession.destroy({
      where: {
        updatedAt: {
          [Op.lt]: cutoffDate,
        },
      },
    });
  }

  static async getSessionStats(): Promise<{
    totalSessions: number;
    activeSessions: number;
    averageMessages: number;
  }> {
    const totalSessions = await GuestSession.count();

    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);

    const activeSessions = await GuestSession.count({
      where: {
        updatedAt: {
          [Op.gte]: oneDayAgo,
        },
      },
    });

    const result = await GuestSession.findOne({
      attributes: [
        [fn('AVG', col('messageCount')), 'averageMessages'],
      ],
    });

    const averageMessages = parseFloat(result?.get('averageMessages') as string) || 0;

    return {
      totalSessions,
      activeSessions,
      averageMessages,
    };
  }
}

GuestSession.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    sessionId: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
    },
    messageCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    ipAddress: {
      type: DataTypes.STRING(45), // IPv6 compatible
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: chatDatabase,
    modelName: 'GuestSession',
    tableName: 'guest_sessions',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['session_id'],
      },
      {
        fields: ['message_count'],
      },
      {
        fields: ['ip_address'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
    ],
  }
);
