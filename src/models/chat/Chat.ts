import { DataTypes, Model, Optional } from 'sequelize';
import { chatDatabase } from '../../config/database';
import { ChatAttributes } from '../../types';

interface ChatCreationAttributes extends Optional<ChatAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class Chat extends Model<ChatAttributes, ChatCreationAttributes> implements ChatAttributes {
  public id!: string;
  public userId?: string;
  public sessionId!: string;
  public title?: string;
  public isGuest!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Static methods
  static async createChat(data: {
    userId?: string;
    sessionId: string;
    title?: string;
    isGuest: boolean;
  }): Promise<Chat> {
    const { EncryptionUtil } = await import('../../utils/encryption');

    return Chat.create({
      id: EncryptionUtil.generateUUID(),
      userId: data.userId,
      sessionId: data.sessionId,
      title: data.title,
      isGuest: data.isGuest,
    });
  }

  static async findBySessionId(sessionId: string): Promise<Chat | null> {
    return Chat.findOne({
      where: { sessionId },
    });
  }

  static async findUserChats(userId: string, limit: number = 20, offset: number = 0): Promise<Chat[]> {
    return Chat.findAll({
      where: {
        userId,
        isGuest: false,
      },
      order: [['updatedAt', 'DESC']],
      limit,
      offset,
    });
  }

  static async findGuestChats(sessionId: string): Promise<Chat[]> {
    return Chat.findAll({
      where: {
        sessionId,
        isGuest: true,
      },
      order: [['createdAt', 'ASC']],
    });
  }

  public async updateTitle(title: string): Promise<void> {
    this.title = title;
    await this.save();
  }

  public async convertToUserChat(userId: string): Promise<void> {
    this.userId = userId;
    this.isGuest = false;
    await this.save();
  }
}

Chat.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    sessionId: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    isGuest: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: chatDatabase,
    modelName: 'Chat',
    tableName: 'chats',
    timestamps: true,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['session_id'],
      },
      {
        fields: ['is_guest'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
    ],
  }
);
