import { DataTypes, Model, Optional } from 'sequelize';
import { userDatabase } from '../../config/database';
import { UserProfileAttributes } from '../../types';
import { User } from './User';

interface UserProfileCreationAttributes extends Optional<UserProfileAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class UserProfile extends Model<UserProfileAttributes, UserProfileCreationAttributes> implements UserProfileAttributes {
  public id!: string;
  public userId!: string;
  public firstName?: string;
  public lastName?: string;
  public profilePicture?: string;
  public plan!: 'FREE' | 'PREMIUM' | 'ENTERPRISE';
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Instance methods
  public getFullName(): string {
    if (this.firstName && this.lastName) {
      return `${this.firstName} ${this.lastName}`;
    }
    return this.firstName || this.lastName || 'User';
  }

  public async updateProfile(data: {
    firstName?: string;
    lastName?: string;
    profilePicture?: string;
  }): Promise<void> {
    if (data.firstName !== undefined) this.firstName = data.firstName;
    if (data.lastName !== undefined) this.lastName = data.lastName;
    if (data.profilePicture !== undefined) this.profilePicture = data.profilePicture;
    await this.save();
  }

  public async updatePlan(plan: 'FREE' | 'PREMIUM' | 'ENTERPRISE'): Promise<void> {
    this.plan = plan;
    await this.save();
  }

  // Static methods
  static async createUserProfile(data: {
    userId: string;
    firstName?: string;
    lastName?: string;
    profilePicture?: string;
    plan?: 'FREE' | 'PREMIUM' | 'ENTERPRISE';
  }): Promise<UserProfile> {
    const { EncryptionUtil } = await import('../../utils/encryption');

    return UserProfile.create({
      id: EncryptionUtil.generateUUID(),
      userId: data.userId,
      firstName: data.firstName,
      lastName: data.lastName,
      profilePicture: data.profilePicture,
      plan: data.plan || 'FREE',
    });
  }

  static async findByUserId(userId: string): Promise<UserProfile | null> {
    return UserProfile.findOne({
      where: { userId },
    });
  }

  static async findOrCreateByUserId(userId: string, defaults?: {
    firstName?: string;
    lastName?: string;
    profilePicture?: string;
    plan?: 'FREE' | 'PREMIUM' | 'ENTERPRISE';
  }): Promise<[UserProfile, boolean]> {
    const [userProfile, created] = await UserProfile.findOrCreate({
      where: { userId },
      defaults: {
        id: (await import('../../utils/encryption')).EncryptionUtil.generateUUID(),
        userId,
        firstName: defaults?.firstName,
        lastName: defaults?.lastName,
        profilePicture: defaults?.profilePicture,
        plan: defaults?.plan || 'FREE',
      },
    });
    return [userProfile, created];
  }
}

UserProfile.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
      onDelete: 'CASCADE',
      unique: true, // One profile per user
    },
    firstName: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    lastName: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    profilePicture: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    plan: {
      type: DataTypes.ENUM('FREE', 'PREMIUM', 'ENTERPRISE'),
      allowNull: false,
      defaultValue: 'FREE',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'UserProfile',
    tableName: 'user_profiles',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id'],
      },
      {
        fields: ['plan'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
    ],
  }
);

// Define associations
User.hasOne(UserProfile, { foreignKey: 'user_id', as: 'profile' });
UserProfile.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
