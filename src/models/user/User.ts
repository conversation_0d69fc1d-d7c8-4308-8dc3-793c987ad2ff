import { DataTypes, Model, Optional, Op } from 'sequelize';
import { userDatabase } from '../../config/database';
import { UserAttributes } from '../../types';

interface UserCreationAttributes extends Optional<UserAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
  public id!: string;
  public email?: string;
  public mobile?: string;
  public password?: string;
  public isActive!: boolean;
  public isVerified!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Instance methods
  public async setPassword(password: string): Promise<void> {
    const { EncryptionUtil } = await import('../../utils/encryption');
    this.password = await EncryptionUtil.hashPassword(password);
  }

  public async validatePassword(password: string): Promise<boolean> {
    if (!this.password) return false;
    const { EncryptionUtil } = await import('../../utils/encryption');
    return EncryptionUtil.comparePassword(password, this.password);
  }

  public async markAsVerified(): Promise<void> {
    this.isVerified = true;
    await this.save();
  }

  public toJSON(): Partial<UserAttributes> {
    const values = { ...this.get() };
    delete values.password; // Never return password in JSON
    return values;
  }

  // Static methods
  static async findByEmailOrMobile(identifier: string): Promise<User | null> {
    const { VALIDATION_RULES } = await import('../../utils/constants');

    const isEmail = VALIDATION_RULES.EMAIL_REGEX.test(identifier);
    const isMobile = VALIDATION_RULES.MOBILE_REGEX.test(identifier);

    if (!isEmail && !isMobile) {
      return null;
    }

    return User.findOne({
      where: isEmail ? { email: identifier } : { mobile: identifier },
    });
  }

  static async createUser(userData: {
    email?: string;
    mobile?: string;
    password?: string;
    isVerified?: boolean;
  }): Promise<User> {
    const { EncryptionUtil } = await import('../../utils/encryption');

    const user = new User({
      id: EncryptionUtil.generateUUID(),
      email: userData.email,
      mobile: userData.mobile,
      isActive: true,
      isVerified: userData.isVerified ?? false,
    });

    if (userData.password) {
      await user.setPassword(userData.password);
    }

    return user.save();
  }
}

User.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: true,
      unique: true,
      validate: {
        isEmail: true,
      },
    },
    mobile: {
      type: DataTypes.STRING(20),
      allowNull: true,
      unique: true,
      validate: {
        is: /^[+]?[1-9][\d]{7,14}$/,
      },
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    isVerified: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'User',
    tableName: 'users',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['email'],
        where: {
          email: {
            [Op.ne]: null,
          },
        },
      },
      {
        unique: true,
        fields: ['mobile'],
        where: {
          mobile: {
            [Op.ne]: null,
          },
        },
      },
    ],
    validate: {
      emailOrMobileRequired() {
        if (!this.email && !this.mobile) {
          throw new Error('Either email or mobile number is required');
        }
      },
    },
  }
);
