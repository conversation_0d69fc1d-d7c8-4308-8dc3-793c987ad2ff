export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  REQUEST_TIMEOUT: 408,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
} as const;

export const ERROR_MESSAGES = {
  INVALID_CREDENTIALS: 'Invalid credentials provided',
  USER_NOT_FOUND: 'User not found',
  USER_ALREADY_EXISTS: 'User already exists',
  INVALID_OTP: 'Invalid or expired OTP',
  OTP_EXPIRED: 'O<PERSON> has expired',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',
  VALIDATION_ERROR: 'Validation error',
  INTERNAL_ERROR: 'Internal server error',
  GUEST_LIMIT_EXCEEDED: 'Guest chat limit exceeded. Please login to continue.',
  CHAT_NOT_FOUND: 'Chat not found',
  INVALID_SESSION: 'Invalid session',
  LLM_ERROR: 'Error processing your request',
  INSUFFICIENT_CREDITS: 'Insufficient credits. Please purchase more credits to continue chatting.',
  CREDIT_DEDUCTION_FAILED: 'Failed to deduct credits',
} as const;

export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful',
  OTP_SENT: 'OTP sent successfully',
  OTP_VERIFIED: 'OTP verified successfully',
  CHAT_CREATED: 'Chat created successfully',
  MESSAGE_SENT: 'Message sent successfully',
  USER_CREATED: 'User created successfully',
  REGISTRATION_INITIATED: 'Registration initiated. Please verify your account.',
  REGISTRATION_VERIFIED: 'Registration completed successfully',
} as const;

export const VALIDATION_RULES = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  MOBILE_REGEX: /^[+]?[1-9][\d]{7,14}$/,
  PASSWORD_MIN_LENGTH: 8,
  OTP_LENGTH: 6,
  OTP_EXPIRY_MINUTES: 5,
  GUEST_CHAT_LIMIT: 5,
} as const;

export const CREDIT_SYSTEM = {
  INITIAL_CREDITS: 50,
  CHAT_MESSAGE_COST: 1,
  TRANSACTION_TYPES: {
    CREDIT: 'CREDIT',
    DEBIT: 'DEBIT',
  },
  DESCRIPTIONS: {
    INITIAL_SIGNUP: 'Initial signup bonus',
    CHAT_MESSAGE: 'Chat message',
    ADMIN_CREDIT: 'Admin credit adjustment',
    PURCHASE: 'Credit purchase',
  },
} as const;

export const USER_PLANS = {
  FREE: 'FREE',
  PREMIUM: 'PREMIUM',
  ENTERPRISE: 'ENTERPRISE',
} as const;

export const LLM_MODELS = {
  OPENAI: {
    GPT_3_5_TURBO: 'gpt-3.5-turbo',
    GPT_4: 'gpt-4',
    GPT_4_TURBO: 'gpt-4-turbo-preview',
  },
  ANTHROPIC: {
    CLAUDE_3_HAIKU: 'claude-3-haiku-********',
    CLAUDE_3_SONNET: 'claude-3-sonnet-20240229',
    CLAUDE_3_OPUS: 'claude-3-opus-20240229',
  },
} as const;

export const DEFAULT_LLM_CONFIG = {
  temperature: 0.7,
  maxTokens: 1000,
} as const;
