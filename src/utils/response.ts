import { Response } from 'express';
import { ApiResponse } from '../types';
import { HTTP_STATUS } from './constants';

export class ResponseUtil {
  static success<T>(
    res: Response,
    message: string,
    data?: T,
    statusCode: number = HTTP_STATUS.OK
  ): Response<ApiResponse<T>> {
    return res.status(statusCode).json({
      success: true,
      message,
      data,
      timestamp: new Date().toISOString(),
    });
  }

  static error(
    res: Response,
    message: string,
    error?: string,
    statusCode: number = HTTP_STATUS.INTERNAL_SERVER_ERROR
  ): Response<ApiResponse> {
    return res.status(statusCode).json({
      success: false,
      message,
      error,
      timestamp: new Date().toISOString(),
    });
  }

  static validationError(
    res: Response,
    message: string,
    error?: string
  ): Response<ApiResponse> {
    return this.error(res, message, error, HTTP_STATUS.BAD_REQUEST);
  }

  static unauthorized(
    res: Response,
    message: string = 'Unauthorized'
  ): Response<ApiResponse> {
    return this.error(res, message, undefined, HTTP_STATUS.UNAUTHORIZED);
  }

  static forbidden(
    res: Response,
    message: string = 'Forbidden'
  ): Response<ApiResponse> {
    return this.error(res, message, undefined, HTTP_STATUS.FORBIDDEN);
  }

  static notFound(
    res: Response,
    message: string = 'Not Found'
  ): Response<ApiResponse> {
    return this.error(res, message, undefined, HTTP_STATUS.NOT_FOUND);
  }

  static conflict(
    res: Response,
    message: string
  ): Response<ApiResponse> {
    return this.error(res, message, undefined, HTTP_STATUS.CONFLICT);
  }
}
