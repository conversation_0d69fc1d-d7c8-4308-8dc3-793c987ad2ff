import { Router } from 'express';
import { UserController } from '../controllers/UserController';
import { validate, validationSchemas, validateQuery } from '../middleware/validation';
import { authenticateToken } from '../middleware/auth';
import Jo<PERSON> from 'joi';

const router = Router();

// All user routes require authentication
router.use(authenticateToken);

// Profile management
router.get('/profile',
  UserController.getProfile
);

router.put('/profile',
  validate(Joi.object({
    email: Joi.string().email().optional(),
    mobile: Joi.string().pattern(/^[+]?[1-9][\d]{7,14}$/).optional(),
  }).custom((value, helpers) => {
    if (!value.email && !value.mobile) {
      return helpers.error('custom.emailOrMobile');
    }
    return value;
  }).messages({
    'custom.emailOrMobile': 'Either email or mobile number is required',
  })),
  UserController.updateProfile
);

// User statistics and activity
router.get('/stats',
  UserController.getUserStats
);

router.get('/activity',
  validateQuery(validationSchemas.pagination),
  UserController.getActivityLog
);

// User preferences
router.get('/preferences',
  UserController.getPreferences
);

router.put('/preferences',
  validate(Joi.object({
    defaultLLMModel: Joi.string().optional(),
    theme: Joi.string().valid('light', 'dark').optional(),
    language: Joi.string().length(2).optional(),
    notifications: Joi.object({
      email: Joi.boolean().optional(),
      push: Joi.boolean().optional(),
    }).optional(),
  })),
  UserController.updatePreferences
);

// Data export
router.get('/export',
  UserController.exportUserData
);

// Account management
router.post('/deactivate',
  UserController.deactivateAccount
);

router.delete('/delete',
  validate(Joi.object({
    password: Joi.string().optional(),
  })),
  UserController.deleteAccount
);

export default router;
