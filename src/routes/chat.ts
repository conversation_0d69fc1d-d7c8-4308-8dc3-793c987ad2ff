import { Router } from 'express';
import <PERSON><PERSON> from 'joi';
import { Chat<PERSON>ontroller } from '../controllers/ChatController';
import { validate, validationSchemas, validateQuery, validateParams } from '../middleware/validation';
import { authenticateToken, optionalAuth, extractSessionId, extractClientIP, rateLimitPerUser } from '../middleware/auth';
import { CSRFProtection } from '../middleware/security';

const router = Router();

// Chat message endpoint (supports both authenticated and guest users)
router.post('/message',
  extractSessionId,
  extractClientIP,
  optionalAuth,
  validate(validationSchemas.chatMessage),
  rateLimitPerUser(20, 60 * 1000), // 20 messages per minute
  CSRFProtection.protect(),
  ChatController.sendMessage
);

// Guest session info
router.get('/guest/:sessionId',
  validateParams(validationSchemas.chatId.keys({ sessionId: validationSchemas.chatId.extract('chatId') })),
  ChatController.getGuestSessionInfo
);

router.get('/guest-info',
  ChatController.getGuestSessionInfo
);

// Protected routes (authentication required)
router.get('/chats',
  authenticateToken,
  validateQuery(validationSchemas.pagination),
  ChatController.getUserChats
);

router.get('/chats/search',
  authenticateToken,
  validateQuery(validationSchemas.pagination.keys({
    query: validationSchemas.chatMessage.extract('message').required()
  })),
  ChatController.searchChats
);

router.get('/chats/:chatId/messages',
  authenticateToken,
  validateParams(validationSchemas.chatId),
  validateQuery(validationSchemas.pagination),
  ChatController.getChatMessages
);

router.put('/chats/:chatId/title',
  authenticateToken,
  validateParams(validationSchemas.chatId),
  validate(Joi.object({
    title: Joi.string().max(100).required()
  })),
  ChatController.updateChatTitle
);

router.delete('/chats/:chatId',
  authenticateToken,
  validateParams(validationSchemas.chatId),
  ChatController.deleteChat
);

router.get('/chats/:chatId/export',
  authenticateToken,
  validateParams(validationSchemas.chatId),
  ChatController.exportChat
);

// Convert guest chat to user chat (when user logs in)
router.post('/convert-guest-chat',
  authenticateToken,
  validate(Joi.object({
    sessionId: Joi.string().required()
  })),
  ChatController.convertGuestChat
);

// LLM model management
router.get('/models',
  ChatController.getAvailableModels
);

router.post('/models/test',
  rateLimitPerUser(5, 60 * 1000), // 5 tests per minute
  validate(Joi.object({
    model: Joi.string().optional(),
    message: Joi.string().required()
  })),
  ChatController.testModel
);

// Admin/Stats routes
router.get('/stats',
  authenticateToken,
  ChatController.getChatStats
);

export default router;
