import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { BaseLanguageModel } from '@langchain/core/language_models/base';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { LLMConfig, LL<PERSON>rovider } from '../types';
import { LLM_MODELS, DEFAULT_LLM_CONFIG } from '../utils/constants';
import logger from '../config/logger';

export class LLMService {
  private static models: Map<string, BaseLanguageModel> = new Map();

  /**
   * Initialize LLM models
   */
  static initialize(): void {
    try {
      // Initialize OpenAI models
      if (process.env.OPENAI_API_KEY) {
        this.initializeOpenAIModels();
      }

      // Initialize Anthropic models
      if (process.env.ANTHROPIC_API_KEY) {
        this.initializeAnthropicModels();
      }

      logger.info('LLM Service initialized successfully');
    } catch (error) {
      logger.error('Error initializing LLM Service:', error);
      throw error;
    }
  }

  /**
   * Initialize OpenAI models
   */
  private static initializeOpenAIModels(): void {
    const openAIModels = Object.values(LLM_MODELS.OPENAI);

    openAIModels.forEach(modelName => {
      const model = new ChatOpenAI({
        modelName,
        temperature: DEFAULT_LLM_CONFIG.temperature,
        maxTokens: DEFAULT_LLM_CONFIG.maxTokens,
        openAIApiKey: process.env.OPENAI_API_KEY,
      });

      this.models.set(modelName, model);
      logger.debug(`Initialized OpenAI model: ${modelName}`);
    });
  }

  /**
   * Initialize Anthropic models
   */
  private static initializeAnthropicModels(): void {
    const anthropicModels = Object.values(LLM_MODELS.ANTHROPIC);

    anthropicModels.forEach(modelName => {
      const model = new ChatAnthropic({
        modelName,
        temperature: DEFAULT_LLM_CONFIG.temperature,
        maxTokens: DEFAULT_LLM_CONFIG.maxTokens,
        anthropicApiKey: process.env.ANTHROPIC_API_KEY,
      });

      this.models.set(modelName, model);
      logger.debug(`Initialized Anthropic model: ${modelName}`);
    });
  }

  /**
   * Get LLM model by name
   */
  static getModel(modelName?: string): BaseLanguageModel {
    const defaultModel = process.env.DEFAULT_LLM_MODEL || LLM_MODELS.OPENAI.GPT_3_5_TURBO;
    const targetModel = modelName || defaultModel;

    const model = this.models.get(targetModel);
    if (!model) {
      throw new Error(`LLM model '${targetModel}' not found or not initialized`);
    }

    return model;
  }

  /**
   * Create a custom model with specific configuration
   */
  static createCustomModel(config: LLMConfig): BaseLanguageModel {
    const provider = this.detectProvider(config.model);

    switch (provider) {
      case LLMProvider.OPENAI:
        return new ChatOpenAI({
          modelName: config.model,
          temperature: config.temperature || DEFAULT_LLM_CONFIG.temperature,
          maxTokens: config.maxTokens || DEFAULT_LLM_CONFIG.maxTokens,
          openAIApiKey: config.apiKey || process.env.OPENAI_API_KEY,
        });

      case LLMProvider.ANTHROPIC:
        return new ChatAnthropic({
          modelName: config.model,
          temperature: config.temperature || DEFAULT_LLM_CONFIG.temperature,
          maxTokens: config.maxTokens || DEFAULT_LLM_CONFIG.maxTokens,
          anthropicApiKey: config.apiKey || process.env.ANTHROPIC_API_KEY,
        });

      default:
        throw new Error(`Unsupported LLM provider for model: ${config.model}`);
    }
  }

  /**
   * Generate chat response
   */
  static async generateResponse(
    message: string,
    modelName?: string,
    systemPrompt?: string,
    conversationHistory?: Array<{ role: 'user' | 'assistant'; content: string }>
  ): Promise<string> {
    try {
      const model = this.getModel(modelName);
      const messages = [];

      // Add system message if provided
      if (systemPrompt) {
        messages.push(new SystemMessage(systemPrompt));
      }

      // Add conversation history
      if (conversationHistory && conversationHistory.length > 0) {
        conversationHistory.forEach(msg => {
          if (msg.role === 'user') {
            messages.push(new HumanMessage(msg.content));
          } else {
            messages.push(new SystemMessage(msg.content));
          }
        });
      }

      // Add current user message
      messages.push(new HumanMessage(message));

      const response = await model.invoke(messages);

      logger.info(`LLM response generated using model: ${modelName || 'default'}`);
      return response.content as string;
    } catch (error) {
      logger.error('Error generating LLM response:', error);
      throw new Error('Failed to generate response from LLM');
    }
  }

  /**
   * Detect LLM provider from model name
   */
  private static detectProvider(modelName: string): LLMProvider {
    const openAIModels = Object.values(LLM_MODELS.OPENAI);
    const anthropicModels = Object.values(LLM_MODELS.ANTHROPIC);

    if (openAIModels.includes(modelName as any)) {
      return LLMProvider.OPENAI;
    }

    if (anthropicModels.includes(modelName as any)) {
      return LLMProvider.ANTHROPIC;
    }

    // Default fallback based on model name patterns
    if (modelName.includes('gpt') || modelName.includes('openai')) {
      return LLMProvider.OPENAI;
    }

    if (modelName.includes('claude') || modelName.includes('anthropic')) {
      return LLMProvider.ANTHROPIC;
    }

    throw new Error(`Cannot detect provider for model: ${modelName}`);
  }

  /**
   * Get available models
   */
  static getAvailableModels(): string[] {
    return Array.from(this.models.keys());
  }

  /**
   * Check if model is available
   */
  static isModelAvailable(modelName: string): boolean {
    return this.models.has(modelName);
  }

  /**
   * Get model statistics
   */
  static getModelStats(): { [key: string]: any } {
    return {
      totalModels: this.models.size,
      availableModels: this.getAvailableModels(),
      openAIModels: Object.values(LLM_MODELS.OPENAI),
      anthropicModels: Object.values(LLM_MODELS.ANTHROPIC),
    };
  }
}
