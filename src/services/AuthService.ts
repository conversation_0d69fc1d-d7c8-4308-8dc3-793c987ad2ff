import { User } from '../models/user';
import { J<PERSON><PERSON><PERSON> } from '../config/jwt';
import { OTPService } from './OTPService';
import { CreditService } from './CreditService';
import { UserProfileService } from './UserProfileService';
import { EncryptionUtil } from '../utils/encryption';
import { VALIDATION_RULES, ERROR_MESSAGES, CREDIT_SYSTEM, USER_PLANS } from '../utils/constants';
import { LoginRequest, OTPVerificationRequest, JWTPayload } from '../types';
import logger from '../config/logger';

export class AuthService {
  /**
   * Login with email/mobile and password
   */
  static async loginWithPassword(loginData: LoginRequest): Promise<{
    token: string;
    user: Partial<User>;
  }> {
    try {
      const { identifier, password } = loginData;

      if (!identifier || !password) {
        throw new Error('Identifier and password are required');
      }

      // Find user by email or mobile
      const user = await User.findByEmailOrMobile(identifier);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('User account is deactivated');
      }

      // Check if user is verified
      if (!user.isVerified) {
        throw new Error('User account is not verified. Please verify your account first.');
      }

      // Validate password
      const isValidPassword = await user.validatePassword(password);
      if (!isValidPassword) {
        throw new Error(ERROR_MESSAGES.INVALID_CREDENTIALS);
      }

      // Generate JWT token
      const tokenPayload: Omit<JWTPayload, 'iat' | 'exp'> = {
        userId: user.id,
        email: user.email,
        mobile: user.mobile,
      };

      const token = JWTUtil.generateToken(tokenPayload);

      logger.info(`User logged in successfully: ${user.id}`);

      return {
        token,
        user: user.toJSON(),
      };
    } catch (error) {
      logger.error('Error in password login:', error);
      throw error;
    }
  }

  /**
   * Request OTP for login
   */
  static async requestOTP(identifier: string): Promise<{
    message: string;
    expiresAt?: Date;
  }> {
    try {
      if (!identifier) {
        throw new Error('Identifier is required');
      }

      // Validate identifier format
      const isEmail = VALIDATION_RULES.EMAIL_REGEX.test(identifier);
      const isMobile = VALIDATION_RULES.MOBILE_REGEX.test(identifier);

      if (!isEmail && !isMobile) {
        throw new Error('Invalid email or mobile number format');
      }

      // Find or create user
      let user = await User.findByEmailOrMobile(identifier);

      if (!user) {
        // Create new user for OTP login
        const userData = isEmail ? { email: identifier } : { mobile: identifier };
        user = await User.createUser(userData);
        logger.info(`New user created for OTP login: ${user.id}`);
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('User account is deactivated');
      }

      // Check if user already has a pending OTP
      const hasPendingOTP = await OTPService.hasPendingOTP(user.id);
      if (hasPendingOTP) {
        const expiryTime = await OTPService.getOTPExpiryTime(user.id);
        throw new Error(`OTP already sent. Please wait until ${expiryTime?.toLocaleTimeString()} or try again later.`);
      }

      // Generate and send OTP
      const otp = await OTPService.generateOTP(user.id);
      const otpSent = await OTPService.sendOTP(identifier, otp);

      if (!otpSent) {
        throw new Error('Failed to send OTP');
      }

      const expiryTime = await OTPService.getOTPExpiryTime(user.id);

      logger.info(`OTP requested for user: ${user.id}`);

      return {
        message: 'OTP sent successfully',
        expiresAt: expiryTime || undefined,
      };
    } catch (error) {
      logger.error('Error requesting OTP:', error);
      throw error;
    }
  }

  /**
   * Verify OTP and login
   */
  static async verifyOTPAndLogin(otpData: OTPVerificationRequest): Promise<{
    token: string;
    user: Partial<User>;
  }> {
    try {
      const { identifier, otp } = otpData;

      if (!identifier || !otp) {
        throw new Error('Identifier and OTP are required');
      }

      // Find user
      const user = await User.findByEmailOrMobile(identifier);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('User account is deactivated');
      }

      // Verify OTP
      const isValidOTP = await OTPService.verifyOTP(user.id, otp);
      if (!isValidOTP) {
        throw new Error(ERROR_MESSAGES.INVALID_OTP);
      }

      // If user is not verified, mark them as verified after successful OTP login
      if (!user.isVerified) {
        await user.markAsVerified();

        // Initialize user credits (50 credits for new users)
        try {
          await CreditService.initializeUserCredits(user.id, CREDIT_SYSTEM.INITIAL_CREDITS);
        } catch (error) {
          logger.error(`Failed to initialize credits for user ${user.id}:`, error);
        }

        // Initialize user profile
        try {
          await UserProfileService.initializeUserProfile(user.id, { plan: USER_PLANS.FREE });
        } catch (error) {
          logger.error(`Failed to initialize profile for user ${user.id}:`, error);
        }

        logger.info(`User auto-verified via OTP login: ${user.id}`);
      }

      // Generate JWT token
      const tokenPayload: Omit<JWTPayload, 'iat' | 'exp'> = {
        userId: user.id,
        email: user.email,
        mobile: user.mobile,
      };

      const token = JWTUtil.generateToken(tokenPayload);

      logger.info(`User logged in via OTP: ${user.id}`);

      return {
        token,
        user: user.toJSON(),
      };
    } catch (error) {
      logger.error('Error in OTP verification:', error);
      throw error;
    }
  }

  /**
   * Register new user with password (creates unverified user and sends OTP)
   */
  static async register(userData: {
    email?: string;
    mobile?: string;
    password: string;
  }): Promise<{
    message: string;
    userId: string;
    expiresAt: Date;
  }> {
    try {
      const { email, mobile, password } = userData;

      if (!email && !mobile) {
        throw new Error('Either email or mobile number is required');
      }

      if (!password || password.length < VALIDATION_RULES.PASSWORD_MIN_LENGTH) {
        throw new Error(`Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters long`);
      }

      // Check if user already exists
      const identifier = email || mobile!;
      const existingUser = await User.findByEmailOrMobile(identifier);
      if (existingUser) {
        if (existingUser.isVerified) {
          throw new Error(ERROR_MESSAGES.USER_ALREADY_EXISTS);
        } else {
          // User exists but not verified, resend OTP
          const otp = await OTPService.generateOTP(existingUser.id);
          await OTPService.sendOTP(identifier, otp);

          const expiryMinutes = parseInt(process.env.OTP_EXPIRY_MINUTES || '5');
          const expiresAt = new Date();
          expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

          return {
            message: 'User already exists but not verified. OTP sent for verification.',
            userId: existingUser.id,
            expiresAt,
          };
        }
      }

      // Create new unverified user
      const user = await User.createUser({ email, mobile, password, isVerified: false });

      // Generate and send OTP
      const otp = await OTPService.generateOTP(user.id);
      await OTPService.sendOTP(identifier, otp);

      const expiryMinutes = parseInt(process.env.OTP_EXPIRY_MINUTES || '5');
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

      logger.info(`New user registered (unverified): ${user.id}`);

      return {
        message: 'User registered successfully. Please verify your account with the OTP sent to your email/mobile.',
        userId: user.id,
        expiresAt,
      };
    } catch (error) {
      logger.error('Error in user registration:', error);
      throw error;
    }
  }

  /**
   * Verify registration OTP and complete user registration
   */
  static async verifyRegistration(otpData: {
    identifier: string;
    otp: string;
  }): Promise<{
    token: string;
    user: Partial<User>;
  }> {
    try {
      const { identifier, otp } = otpData;

      // Find user by identifier
      const user = await User.findByEmailOrMobile(identifier);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Check if user is already verified
      if (user.isVerified) {
        throw new Error('User is already verified');
      }

      // Verify OTP
      const isValidOTP = await OTPService.verifyOTP(user.id, otp);
      if (!isValidOTP) {
        throw new Error('Invalid or expired OTP');
      }

      // Mark user as verified
      await user.markAsVerified();

      // Initialize user credits (50 credits for new users)
      try {
        await CreditService.initializeUserCredits(user.id, CREDIT_SYSTEM.INITIAL_CREDITS);
      } catch (error) {
        logger.error(`Failed to initialize credits for user ${user.id}:`, error);
        // Don't fail the verification if credit initialization fails
      }

      // Initialize user profile
      try {
        await UserProfileService.initializeUserProfile(user.id, { plan: USER_PLANS.FREE });
      } catch (error) {
        logger.error(`Failed to initialize profile for user ${user.id}:`, error);
        // Don't fail the verification if profile initialization fails
      }

      // Generate JWT token
      const tokenPayload: Omit<JWTPayload, 'iat' | 'exp'> = {
        userId: user.id,
        email: user.email,
        mobile: user.mobile,
      };

      const token = JWTUtil.generateToken(tokenPayload);

      logger.info(`User registration verified: ${user.id}`);

      return {
        token,
        user: user.toJSON(),
      };
    } catch (error) {
      logger.error('Error in registration verification:', error);
      throw error;
    }
  }

  /**
   * Verify JWT token
   */
  static async verifyToken(token: string): Promise<JWTPayload> {
    try {
      const payload = JWTUtil.verifyToken(token);

      // Verify user still exists, is active, and is verified
      const user = await User.findByPk(payload.userId);
      if (!user || !user.isActive) {
        throw new Error('User not found or inactive');
      }

      if (!user.isVerified) {
        throw new Error('User account is not verified');
      }

      return payload;
    } catch (error) {
      logger.error('Error verifying token:', error);
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Refresh JWT token
   */
  static async refreshToken(token: string): Promise<string> {
    try {
      const payload = await this.verifyToken(token);

      // Generate new token with same payload
      const newTokenPayload: Omit<JWTPayload, 'iat' | 'exp'> = {
        userId: payload.userId,
        email: payload.email,
        mobile: payload.mobile,
      };

      const newToken = JWTUtil.generateToken(newTokenPayload);

      logger.info(`Token refreshed for user: ${payload.userId}`);

      return newToken;
    } catch (error) {
      logger.error('Error refreshing token:', error);
      throw error;
    }
  }

  /**
   * Change user password
   */
  static async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    try {
      if (!newPassword || newPassword.length < VALIDATION_RULES.PASSWORD_MIN_LENGTH) {
        throw new Error(`Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters long`);
      }

      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Verify current password if user has one
      if (user.password) {
        const isValidPassword = await user.validatePassword(currentPassword);
        if (!isValidPassword) {
          throw new Error('Current password is incorrect');
        }
      }

      // Set new password
      await user.setPassword(newPassword);
      await user.save();

      logger.info(`Password changed for user: ${userId}`);
    } catch (error) {
      logger.error('Error changing password:', error);
      throw error;
    }
  }
}
