import { UserProfile } from '../models/user';
import logger from '../config/logger';

export class UserProfileService {
  /**
   * Initialize user profile (called when user is first verified)
   */
  static async initializeUserProfile(userId: string, data?: {
    firstName?: string;
    lastName?: string;
    profilePicture?: string;
    plan?: 'FREE' | 'PREMIUM' | 'ENTERPRISE';
  }): Promise<UserProfile> {
    try {
      // Check if user already has a profile
      const existingProfile = await UserProfile.findByUserId(userId);
      if (existingProfile) {
        logger.info(`User ${userId} already has a profile`);
        return existingProfile;
      }

      // Create new profile
      const userProfile = await UserProfile.createUserProfile({
        userId,
        firstName: data?.firstName,
        lastName: data?.lastName,
        profilePicture: data?.profilePicture,
        plan: data?.plan || 'FREE',
      });

      logger.info(`Initialized profile for user ${userId}`);
      return userProfile;
    } catch (error) {
      logger.error('Error initializing user profile:', error);
      throw error;
    }
  }

  /**
   * Get user profile
   */
  static async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      return await UserProfile.findByUserId(userId);
    } catch (error) {
      logger.error('Error getting user profile:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  static async updateUserProfile(userId: string, data: {
    firstName?: string;
    lastName?: string;
    profilePicture?: string;
  }): Promise<UserProfile> {
    try {
      const [userProfile] = await UserProfile.findOrCreateByUserId(userId);
      await userProfile.updateProfile(data);
      
      logger.info(`Updated profile for user ${userId}`);
      return userProfile;
    } catch (error) {
      logger.error('Error updating user profile:', error);
      throw error;
    }
  }

  /**
   * Update user plan
   */
  static async updateUserPlan(userId: string, plan: 'FREE' | 'PREMIUM' | 'ENTERPRISE'): Promise<UserProfile> {
    try {
      const [userProfile] = await UserProfile.findOrCreateByUserId(userId);
      await userProfile.updatePlan(plan);
      
      logger.info(`Updated plan to ${plan} for user ${userId}`);
      return userProfile;
    } catch (error) {
      logger.error('Error updating user plan:', error);
      throw error;
    }
  }

  /**
   * Get user profile with credits
   */
  static async getUserProfileWithCredits(userId: string): Promise<{
    profile: UserProfile | null;
    credits: number;
  }> {
    try {
      const { CreditService } = await import('./CreditService');
      
      const [profile, credits] = await Promise.all([
        this.getUserProfile(userId),
        CreditService.getUserCredits(userId),
      ]);

      return { profile, credits };
    } catch (error) {
      logger.error('Error getting user profile with credits:', error);
      throw error;
    }
  }
}
