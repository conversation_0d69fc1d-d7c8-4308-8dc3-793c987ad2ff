import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/AuthService';
import { ResponseUtil } from '../utils/response';
import { ERROR_MESSAGES } from '../utils/constants';
import { AuthenticatedRequest, JWTPayload } from '../types';
import logger from '../config/logger';

/**
 * Middleware to authenticate JWT token
 */
export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    // Verify token
    const payload = await AuthService.verifyToken(token);
    req.user = payload;

    logger.debug(`User authenticated: ${payload.userId}`);
    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
  }
};

/**
 * Optional authentication middleware - doesn't fail if no token
 */
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      try {
        const payload = await AuthService.verifyToken(token);
        req.user = payload;
        logger.debug(`User optionally authenticated: ${payload.userId}`);
      } catch (error) {
        // Token is invalid, but we continue without authentication
        logger.debug('Invalid token in optional auth, continuing without auth');
      }
    }

    next();
  } catch (error) {
    logger.error('Optional authentication error:', error);
    next(); // Continue without authentication
  }
};

/**
 * Middleware to extract session ID for guest users
 */
export const extractSessionId = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    // Try to get session ID from header, query, or body
    const sessionId = 
      req.headers['x-session-id'] as string ||
      req.query.sessionId as string ||
      req.body.sessionId as string;

    if (sessionId) {
      (req as any).sessionId = sessionId;
    }

    next();
  } catch (error) {
    logger.error('Error extracting session ID:', error);
    next();
  }
};

/**
 * Middleware to get client IP address
 */
export const extractClientIP = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const clientIP = 
      req.headers['x-forwarded-for'] as string ||
      req.headers['x-real-ip'] as string ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      '127.0.0.1';

    (req as any).clientIP = Array.isArray(clientIP) ? clientIP[0] : clientIP.split(',')[0];
    next();
  } catch (error) {
    logger.error('Error extracting client IP:', error);
    (req as any).clientIP = '127.0.0.1';
    next();
  }
};

/**
 * Middleware to check if user is active
 */
export const checkUserActive = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    // This check is already done in AuthService.verifyToken
    // but we can add additional checks here if needed
    next();
  } catch (error) {
    logger.error('Error checking user active status:', error);
    ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
  }
};

/**
 * Middleware to validate user permissions
 */
export const requirePermission = (permission: string) => {
  return async (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      if (!req.user) {
        ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
        return;
      }

      // In a more complex system, you would check user roles/permissions here
      // For now, all authenticated users have all permissions
      next();
    } catch (error) {
      logger.error('Error checking user permissions:', error);
      ResponseUtil.forbidden(res, ERROR_MESSAGES.FORBIDDEN);
    }
  };
};

/**
 * Middleware to rate limit requests per user
 */
export const rateLimitPerUser = (maxRequests: number, windowMs: number) => {
  const userRequests = new Map<string, { count: number; resetTime: number }>();

  return (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): void => {
    try {
      const userId = req.user?.userId || (req as any).clientIP || 'anonymous';
      const now = Date.now();

      const userLimit = userRequests.get(userId);
      
      if (!userLimit || now > userLimit.resetTime) {
        // Reset or initialize limit
        userRequests.set(userId, {
          count: 1,
          resetTime: now + windowMs,
        });
        next();
        return;
      }

      if (userLimit.count >= maxRequests) {
        ResponseUtil.error(res, 'Rate limit exceeded', undefined, 429);
        return;
      }

      userLimit.count++;
      next();
    } catch (error) {
      logger.error('Error in rate limiting:', error);
      next();
    }
  };
};
