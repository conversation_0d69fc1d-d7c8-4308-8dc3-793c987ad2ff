import { Request, Response, NextFunction } from 'express';
import { ResponseUtil } from '../utils/response';
import { ERROR_MESSAGES, HTTP_STATUS } from '../utils/constants';
import logger from '../config/logger';

/**
 * Custom error class for application errors
 */
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = HTTP_STATUS.INTERNAL_SERVER_ERROR) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Global error handling middleware
 */
export const globalErrorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode: number = HTTP_STATUS.INTERNAL_SERVER_ERROR;
  let message: string = ERROR_MESSAGES.INTERNAL_ERROR;
  let errorDetails: string | undefined;

  // Handle different types of errors
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
  } else if (error.name === 'ValidationError') {
    statusCode = HTTP_STATUS.BAD_REQUEST;
    message = ERROR_MESSAGES.VALIDATION_ERROR;
    errorDetails = error.message;
  } else if (error.name === 'SequelizeValidationError') {
    statusCode = HTTP_STATUS.BAD_REQUEST;
    message = ERROR_MESSAGES.VALIDATION_ERROR;
    errorDetails = (error as any).errors?.map((e: any) => e.message).join(', ');
  } else if (error.name === 'SequelizeUniqueConstraintError') {
    statusCode = HTTP_STATUS.CONFLICT;
    message = 'Resource already exists';
    errorDetails = (error as any).errors?.map((e: any) => e.message).join(', ');
  } else if (error.name === 'SequelizeForeignKeyConstraintError') {
    statusCode = HTTP_STATUS.BAD_REQUEST;
    message = 'Invalid reference to related resource';
  } else if (error.name === 'SequelizeConnectionError') {
    statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
    message = 'Database connection error';
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = HTTP_STATUS.UNAUTHORIZED;
    message = ERROR_MESSAGES.UNAUTHORIZED;
  } else if (error.name === 'TokenExpiredError') {
    statusCode = HTTP_STATUS.UNAUTHORIZED;
    message = 'Token has expired';
  } else if (error.name === 'SyntaxError' && 'body' in error) {
    statusCode = HTTP_STATUS.BAD_REQUEST;
    message = 'Invalid JSON in request body';
  }

  // Log error details
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    statusCode,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.userId,
  });

  // Send error response
  ResponseUtil.error(res, message, errorDetails, statusCode);
};

/**
 * Handle 404 errors
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new AppError(`Route ${req.originalUrl} not found`, HTTP_STATUS.NOT_FOUND);
  next(error);
};

/**
 * Async error wrapper for route handlers
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Database error handler
 */
export const handleDatabaseError = (error: any): AppError => {
  if (error.name === 'SequelizeValidationError') {
    const message = error.errors.map((e: any) => e.message).join(', ');
    return new AppError(message, HTTP_STATUS.BAD_REQUEST);
  }

  if (error.name === 'SequelizeUniqueConstraintError') {
    const field = error.errors[0]?.path || 'field';
    return new AppError(`${field} already exists`, HTTP_STATUS.CONFLICT);
  }

  if (error.name === 'SequelizeForeignKeyConstraintError') {
    return new AppError('Invalid reference to related resource', HTTP_STATUS.BAD_REQUEST);
  }

  if (error.name === 'SequelizeConnectionError') {
    return new AppError('Database connection failed', HTTP_STATUS.INTERNAL_SERVER_ERROR);
  }

  return new AppError('Database operation failed', HTTP_STATUS.INTERNAL_SERVER_ERROR);
};

/**
 * Authentication error handler
 */
export const handleAuthError = (error: any): AppError => {
  if (error.name === 'JsonWebTokenError') {
    return new AppError(ERROR_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED);
  }

  if (error.name === 'TokenExpiredError') {
    return new AppError('Token has expired', HTTP_STATUS.UNAUTHORIZED);
  }

  return new AppError(ERROR_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED);
};

/**
 * Validation error handler
 */
export const handleValidationError = (error: any): AppError => {
  if (error.isJoi) {
    const message = error.details.map((detail: any) => detail.message).join(', ');
    return new AppError(message, HTTP_STATUS.BAD_REQUEST);
  }

  return new AppError(ERROR_MESSAGES.VALIDATION_ERROR, HTTP_STATUS.BAD_REQUEST);
};

/**
 * LLM service error handler
 */
export const handleLLMError = (error: any): AppError => {
  logger.error('LLM service error:', error);

  if (error.message?.includes('API key')) {
    return new AppError('LLM service configuration error', HTTP_STATUS.INTERNAL_SERVER_ERROR);
  }

  if (error.message?.includes('rate limit')) {
    return new AppError('LLM service rate limit exceeded', HTTP_STATUS.TOO_MANY_REQUESTS);
  }

  if (error.message?.includes('timeout')) {
    return new AppError('LLM service timeout', HTTP_STATUS.REQUEST_TIMEOUT);
  }

  return new AppError(ERROR_MESSAGES.LLM_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR);
};

/**
 * Process uncaught exceptions
 */
process.on('uncaughtException', (error: Error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

/**
 * Process unhandled promise rejections
 */
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

/**
 * Graceful shutdown handler
 */
export const gracefulShutdown = (server: any) => {
  const shutdown = (signal: string) => {
    logger.info(`Received ${signal}. Graceful shutdown...`);

    server.close(() => {
      logger.info('HTTP server closed');

      // Close database connections
      Promise.all([
        // Add database close operations here
      ]).then(() => {
        logger.info('Database connections closed');
        process.exit(0);
      }).catch((error) => {
        logger.error('Error during shutdown:', error);
        process.exit(1);
      });
    });

    // Force close after 30 seconds
    setTimeout(() => {
      logger.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 30000);
  };

  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));
};
