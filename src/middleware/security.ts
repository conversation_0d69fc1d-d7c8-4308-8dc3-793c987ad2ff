import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { ResponseUtil } from '../utils/response';
import logger from '../config/logger';

/**
 * CSRF Protection Middleware
 */
export class CSRFProtection {
  private static tokens = new Map<string, { token: string; expires: number }>();
  private static readonly TOKEN_EXPIRY = 60 * 60 * 1000; // 1 hour

  /**
   * Generate CSRF token
   */
  static generateToken(sessionId: string): string {
    const token = crypto.randomBytes(32).toString('hex');
    const expires = Date.now() + this.TOKEN_EXPIRY;
    
    this.tokens.set(sessionId, { token, expires });
    
    // Clean up expired tokens
    this.cleanupExpiredTokens();
    
    return token;
  }

  /**
   * Verify CSRF token
   */
  static verifyToken(sessionId: string, token: string): boolean {
    const storedToken = this.tokens.get(sessionId);
    
    if (!storedToken) {
      return false;
    }
    
    if (Date.now() > storedToken.expires) {
      this.tokens.delete(sessionId);
      return false;
    }
    
    return storedToken.token === token;
  }

  /**
   * Clean up expired tokens
   */
  private static cleanupExpiredTokens(): void {
    const now = Date.now();
    for (const [sessionId, tokenData] of this.tokens.entries()) {
      if (now > tokenData.expires) {
        this.tokens.delete(sessionId);
      }
    }
  }

  /**
   * Middleware to protect against CSRF attacks
   */
  static protect() {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        // Skip CSRF protection for GET requests
        if (req.method === 'GET') {
          next();
          return;
        }

        // Get session ID from various sources
        const sessionId = 
          req.headers['x-session-id'] as string ||
          req.query.sessionId as string ||
          req.body.sessionId as string ||
          (req as any).sessionId ||
          'default';

        // Get CSRF token from header or body
        const csrfToken = 
          req.headers['x-csrf-token'] as string ||
          req.body.csrfToken as string;

        if (!csrfToken) {
          logger.warn(`CSRF token missing for session: ${sessionId}`);
          ResponseUtil.forbidden(res, 'CSRF token required');
          return;
        }

        if (!this.verifyToken(sessionId, csrfToken)) {
          logger.warn(`Invalid CSRF token for session: ${sessionId}`);
          ResponseUtil.forbidden(res, 'Invalid CSRF token');
          return;
        }

        next();
      } catch (error) {
        logger.error('CSRF protection error:', error);
        ResponseUtil.forbidden(res, 'CSRF protection error');
      }
    };
  }

  /**
   * Endpoint to get CSRF token
   */
  static getToken() {
    return (req: Request, res: Response): void => {
      try {
        const sessionId = 
          req.headers['x-session-id'] as string ||
          req.query.sessionId as string ||
          'default';

        const token = this.generateToken(sessionId);
        
        ResponseUtil.success(res, 'CSRF token generated', { csrfToken: token });
      } catch (error) {
        logger.error('Error generating CSRF token:', error);
        ResponseUtil.error(res, 'Failed to generate CSRF token');
      }
    };
  }
}

/**
 * Request signing middleware for API security
 */
export class RequestSigning {
  private static readonly SECRET = process.env.CSRF_SECRET || 'your_csrf_secret_here';

  /**
   * Generate signature for request
   */
  static generateSignature(
    method: string,
    url: string,
    body: string,
    timestamp: string,
    nonce: string
  ): string {
    const payload = `${method}|${url}|${body}|${timestamp}|${nonce}`;
    return crypto
      .createHmac('sha256', this.SECRET)
      .update(payload)
      .digest('hex');
  }

  /**
   * Verify request signature
   */
  static verifySignature(
    method: string,
    url: string,
    body: string,
    timestamp: string,
    nonce: string,
    signature: string
  ): boolean {
    const expectedSignature = this.generateSignature(method, url, body, timestamp, nonce);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  }

  /**
   * Middleware to verify signed requests
   */
  static verify() {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        // Skip signature verification for development
        if (process.env.NODE_ENV === 'development') {
          next();
          return;
        }

        const signature = req.headers['x-signature'] as string;
        const timestamp = req.headers['x-timestamp'] as string;
        const nonce = req.headers['x-nonce'] as string;

        if (!signature || !timestamp || !nonce) {
          logger.warn('Missing signature headers');
          ResponseUtil.forbidden(res, 'Request signature required');
          return;
        }

        // Check timestamp (prevent replay attacks)
        const requestTime = parseInt(timestamp);
        const currentTime = Date.now();
        const timeDiff = Math.abs(currentTime - requestTime);
        
        if (timeDiff > 5 * 60 * 1000) { // 5 minutes
          logger.warn('Request timestamp too old');
          ResponseUtil.forbidden(res, 'Request timestamp invalid');
          return;
        }

        // Verify signature
        const body = JSON.stringify(req.body || {});
        const isValid = this.verifySignature(
          req.method,
          req.originalUrl,
          body,
          timestamp,
          nonce,
          signature
        );

        if (!isValid) {
          logger.warn('Invalid request signature');
          ResponseUtil.forbidden(res, 'Invalid request signature');
          return;
        }

        next();
      } catch (error) {
        logger.error('Request signing verification error:', error);
        ResponseUtil.forbidden(res, 'Request verification failed');
      }
    };
  }
}

/**
 * Rate limiting middleware
 */
export class RateLimiter {
  private static requests = new Map<string, { count: number; resetTime: number }>();

  /**
   * Rate limiting middleware
   */
  static limit(maxRequests: number, windowMs: number) {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        const clientId = 
          req.headers['x-forwarded-for'] as string ||
          req.connection.remoteAddress ||
          'unknown';

        const now = Date.now();
        const clientLimit = this.requests.get(clientId);

        if (!clientLimit || now > clientLimit.resetTime) {
          // Reset or initialize limit
          this.requests.set(clientId, {
            count: 1,
            resetTime: now + windowMs,
          });
          next();
          return;
        }

        if (clientLimit.count >= maxRequests) {
          logger.warn(`Rate limit exceeded for client: ${clientId}`);
          ResponseUtil.error(res, 'Rate limit exceeded', undefined, 429);
          return;
        }

        clientLimit.count++;
        next();
      } catch (error) {
        logger.error('Rate limiting error:', error);
        next();
      }
    };
  }
}

/**
 * Security headers middleware
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction): void => {
  // Set security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  // Remove server information
  res.removeHeader('X-Powered-By');
  
  next();
};

/**
 * Input sanitization middleware
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // Sanitize request body
    if (req.body && typeof req.body === 'object') {
      sanitizeObject(req.body);
    }

    // Sanitize query parameters
    if (req.query && typeof req.query === 'object') {
      sanitizeObject(req.query);
    }

    next();
  } catch (error) {
    logger.error('Input sanitization error:', error);
    next();
  }
};

/**
 * Recursively sanitize object properties
 */
function sanitizeObject(obj: any): void {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      if (typeof obj[key] === 'string') {
        // Remove potential XSS patterns
        obj[key] = obj[key]
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=/gi, '');
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        sanitizeObject(obj[key]);
      }
    }
  }
}
