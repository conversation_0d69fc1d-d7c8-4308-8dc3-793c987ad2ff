# Server Configuration
PORT=5529
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=24h

# User Database Configuration
USER_DB_HOST=localhost
USER_DB_PORT=3306
USER_DB_NAME=infini_ai_users
USER_DB_USERNAME=inf_ai_user
USER_DB_PASSWORD=inf_ai_user

# Chat Database Configuration
CHAT_DB_HOST=localhost
CHAT_DB_PORT=3306
CHAT_DB_NAME=infini_ai_user_chat_recs
CHAT_DB_USERNAME=inf_ai_chat_recs
CHAT_DB_PASSWORD=inf_ai_chat_recs

# LLM Configuration
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
DEFAULT_LLM_MODEL=gpt-3.5-turbo

# OTP Configuration
OTP_EXPIRY_MINUTES=5

# Security
CSRF_SECRET=your_csrf_secret_here

# Guest Chat Limits
GUEST_CHAT_LIMIT=5
